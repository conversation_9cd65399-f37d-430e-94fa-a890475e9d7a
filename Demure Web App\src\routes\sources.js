const express = require('express');
const Source = require('../models/Source');
const Logger = require('../utils/logger');

function createSourcesRouter(sourceManager) {
  const router = express.Router();
  const logger = new Logger();

  // Get all sources
  router.get('/', async (req, res) => {
    try {
      const sources = await sourceManager.getAllSources();
      res.json({
        success: true,
        data: sources,
        count: sources.length
      });
    } catch (error) {
      logger.error('Error fetching sources:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch sources'
      });
    }
  });

  // Get source by ID
  router.get('/:id', async (req, res) => {
    try {
      const source = await Source.findById(req.params.id);
      
      if (!source) {
        return res.status(404).json({
          success: false,
          error: 'Source not found'
        });
      }

      res.json({
        success: true,
        data: source
      });
    } catch (error) {
      logger.error('Error fetching source:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch source'
      });
    }
  });

  // Create new source
  router.post('/', async (req, res) => {
    try {
      const sourceConfig = req.body;
      
      // Validate required fields
      const requiredFields = ['name', 'type', 'target', 'telegramAccount'];
      const missingFields = requiredFields.filter(field => !sourceConfig[field]);
      
      if (missingFields.length > 0) {
        return res.status(400).json({
          success: false,
          error: `Missing required fields: ${missingFields.join(', ')}`
        });
      }

      const source = await sourceManager.addSource(sourceConfig);
      
      res.status(201).json({
        success: true,
        data: source,
        message: 'Source created successfully'
      });
    } catch (error) {
      logger.error('Error creating source:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to create source'
      });
    }
  });

  // Update source
  router.put('/:id', async (req, res) => {
    try {
      const source = await Source.findById(req.params.id);
      
      if (!source) {
        return res.status(404).json({
          success: false,
          error: 'Source not found'
        });
      }

      // Update allowed fields
      const allowedUpdates = ['name', 'extractionPattern', 'configuration'];
      const updates = {};
      
      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          updates[field] = req.body[field];
        }
      });

      Object.assign(source, updates);
      await source.save();

      res.json({
        success: true,
        data: source,
        message: 'Source updated successfully'
      });
    } catch (error) {
      logger.error('Error updating source:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update source'
      });
    }
  });

  // Toggle source active status
  router.patch('/:id/toggle', async (req, res) => {
    try {
      const source = await Source.findById(req.params.id);
      
      if (!source) {
        return res.status(404).json({
          success: false,
          error: 'Source not found'
        });
      }

      if (source.isActive) {
        await sourceManager.stopListening(source._id.toString());
        await source.setPaused();
      } else {
        await sourceManager.startListening(source);
        await source.setActive();
      }

      res.json({
        success: true,
        data: source,
        message: `Source ${source.isActive ? 'activated' : 'paused'} successfully`
      });
    } catch (error) {
      logger.error('Error toggling source:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to toggle source status'
      });
    }
  });

  // Delete source
  router.delete('/:id', async (req, res) => {
    try {
      const source = await sourceManager.removeSource(req.params.id);
      
      res.json({
        success: true,
        data: source,
        message: 'Source removed successfully'
      });
    } catch (error) {
      logger.error('Error deleting source:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete source'
      });
    }
  });

  // Get source statistics
  router.get('/:id/stats', async (req, res) => {
    try {
      const timeRange = req.query.timeRange || '24h';
      const analytics = await sourceManager.getSourceAnalytics(req.params.id, timeRange);
      
      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Error fetching source stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch source statistics'
      });
    }
  });

  // Get available Telegram accounts
  router.get('/telegram/accounts', (req, res) => {
    try {
      const accounts = sourceManager.telegramPool.getStatus();
      
      res.json({
        success: true,
        data: {
          accounts: Object.keys(accounts.clients).map(id => ({
            id,
            isActive: accounts.clients[id].isActive,
            phoneNumber: accounts.clients[id].phoneNumber,
            requestCount: accounts.clients[id].requestCount,
            errorCount: accounts.clients[id].errorCount,
            lastUsed: accounts.clients[id].lastUsed
          }))
        }
      });
    } catch (error) {
      logger.error('Error fetching Telegram accounts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch Telegram accounts'
      });
    }
  });

  return router;
}

module.exports = createSourcesRouter;
