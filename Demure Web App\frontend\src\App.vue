<template>
  <div id="app">
    <div class="app-container">
      <!-- Sidebar Navigation -->
      <aside class="sidebar">
        <div class="sidebar-header">
          <h1 class="app-title">
            <el-icon><TrendCharts /></el-icon>
            Demure Web App
          </h1>
          <p class="app-subtitle">Token Analysis Platform</p>
        </div>
        
        <nav class="sidebar-nav">
          <el-menu
            :default-active="$route.path"
            router
            class="sidebar-menu"
            background-color="#ffffff"
            text-color="#606266"
            active-text-color="#366092"
          >
            <el-menu-item index="/dashboard">
              <el-icon><Monitor /></el-icon>
              <span>Dashboard</span>
            </el-menu-item>
            
            <el-menu-item index="/sources">
              <el-icon><Connection /></el-icon>
              <span>Sources</span>
            </el-menu-item>
            
            <el-menu-item index="/tokens">
              <el-icon><Coin /></el-icon>
              <span>Tokens</span>
            </el-menu-item>
            
            <el-menu-item index="/analytics">
              <el-icon><DataAnalysis /></el-icon>
              <span>Analytics</span>
            </el-menu-item>
            
            <el-menu-item index="/settings">
              <el-icon><Setting /></el-icon>
              <span>Settings</span>
            </el-menu-item>
          </el-menu>
        </nav>
        
        <!-- Connection Status -->
        <div class="connection-status">
          <el-card class="status-card">
            <div class="status-item">
              <el-icon :class="connectionStatus.connected ? 'text-success' : 'text-danger'">
                <Connection />
              </el-icon>
              <span class="status-text">
                {{ connectionStatus.connected ? 'Connected' : 'Disconnected' }}
              </span>
            </div>
            
            <div class="status-item">
              <el-icon class="text-info"><User /></el-icon>
              <span class="status-text">{{ connectionStatus.accounts }} Accounts</span>
            </div>
            
            <div class="status-item">
              <el-icon class="text-warning"><Clock /></el-icon>
              <span class="status-text">Queue: {{ connectionStatus.queueLength }}</span>
            </div>
          </el-card>
        </div>
      </aside>
      
      <!-- Main Content -->
      <main class="main-content">
        <router-view />
      </main>
    </div>
    
    <!-- Global Loading -->
    <el-loading
      v-loading="globalLoading"
      element-loading-text="Loading..."
      element-loading-background="rgba(0, 0, 0, 0.8)"
    />
    
    <!-- Notifications -->
    <div id="notifications"></div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useSocketStore } from '@/stores/socket'
import { useAppStore } from '@/stores/app'

export default {
  name: 'App',
  setup() {
    const socketStore = useSocketStore()
    const appStore = useAppStore()
    
    const globalLoading = ref(false)
    const connectionStatus = ref({
      connected: false,
      accounts: 0,
      queueLength: 0
    })
    
    onMounted(async () => {
      // Initialize socket connection
      await socketStore.connect()
      
      // Listen for connection status updates
      socketStore.on('connect', () => {
        connectionStatus.value.connected = true
      })
      
      socketStore.on('disconnect', () => {
        connectionStatus.value.connected = false
      })
      
      socketStore.on('stats', (data) => {
        connectionStatus.value.accounts = data.sources?.activeSources?.length || 0
        connectionStatus.value.queueLength = data.tokens?.queueLength || 0
      })
      
      // Load initial app data
      await appStore.loadInitialData()
    })
    
    onUnmounted(() => {
      socketStore.disconnect()
    })
    
    return {
      globalLoading,
      connectionStatus
    }
  }
}
</script>

<style scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.app-title {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-subtitle {
  font-size: 12px;
  opacity: 0.9;
  margin: 0;
}

.sidebar-nav {
  flex: 1;
  overflow-y: auto;
}

.sidebar-menu {
  border: none;
}

.connection-status {
  padding: 16px;
  border-top: 1px solid var(--border-color);
}

.status-card {
  background: #f8f9fa;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-text {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-info {
  color: var(--info-color);
}

.text-warning {
  color: var(--warning-color);
}

@media (max-width: 768px) {
  .sidebar {
    height: auto;
  }
  
  .sidebar-header {
    padding: 16px;
  }
  
  .app-title {
    font-size: 18px;
  }
}
</style>
