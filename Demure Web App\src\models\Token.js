const mongoose = require('mongoose');

const tokenSchema = new mongoose.Schema({
  address: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    index: true
  },
  
  sourceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Source',
    required: true,
    index: true
  },
  
  // Basic token information
  symbol: {
    type: String,
    trim: true,
    maxlength: 20
  },
  
  name: {
    type: String,
    trim: true,
    maxlength: 100
  },
  
  // Financial data from Soul Scanner
  price: {
    type: Number,
    default: 0
  },
  
  marketCap: {
    type: String,
    trim: true
  },
  
  volume24h: {
    type: String,
    trim: true
  },
  
  holders: {
    type: Number,
    default: 0
  },
  
  // Additional metrics
  liquidity: {
    type: String,
    trim: true
  },
  
  fdv: {
    type: String,
    trim: true
  },
  
  // Risk assessment
  riskScore: {
    type: Number,
    min: 0,
    max: 10,
    default: 5
  },
  
  riskFactors: [{
    factor: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    description: String
  }],
  
  // Analysis status
  analysisStatus: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'timeout'],
    default: 'pending',
    index: true
  },
  
  analysisAttempts: {
    type: Number,
    default: 0
  },
  
  lastAnalysisAttempt: {
    type: Date
  },
  
  analysisError: {
    message: String,
    code: String,
    timestamp: Date
  },
  
  // Soul Scanner raw response
  soulScannerResponse: {
    rawText: String,
    parsedData: mongoose.Schema.Types.Mixed,
    responseTime: Number,
    timestamp: Date
  },
  
  // Discovery information
  discoveredAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  discoveredMessage: {
    text: String,
    messageId: String,
    chatId: String
  },
  
  // Performance tracking
  priceHistory: [{
    price: Number,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Tags and categories
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  category: {
    type: String,
    enum: ['meme', 'defi', 'nft', 'gaming', 'utility', 'unknown'],
    default: 'unknown'
  },
  
  // Metadata
  metadata: {
    website: String,
    twitter: String,
    telegram: String,
    description: String
  },
  
  // Flags
  isVerified: {
    type: Boolean,
    default: false
  },
  
  isFlagged: {
    type: Boolean,
    default: false
  },
  
  flagReason: String,
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
tokenSchema.index({ sourceId: 1, discoveredAt: -1 });
tokenSchema.index({ analysisStatus: 1, createdAt: -1 });
tokenSchema.index({ riskScore: 1 });
tokenSchema.index({ price: 1 });
tokenSchema.index({ holders: 1 });
tokenSchema.index({ category: 1 });
tokenSchema.index({ tags: 1 });

// Compound indexes
tokenSchema.index({ sourceId: 1, analysisStatus: 1 });
tokenSchema.index({ discoveredAt: -1, analysisStatus: 1 });

// Virtual for age in hours
tokenSchema.virtual('ageInHours').get(function() {
  return Math.round((Date.now() - this.discoveredAt.getTime()) / (1000 * 60 * 60));
});

// Virtual for price change
tokenSchema.virtual('priceChange24h').get(function() {
  if (this.priceHistory.length < 2) return 0;
  
  const now = Date.now();
  const dayAgo = now - (24 * 60 * 60 * 1000);
  
  const recentPrice = this.priceHistory[this.priceHistory.length - 1].price;
  const oldPrice = this.priceHistory.find(p => p.timestamp.getTime() >= dayAgo)?.price;
  
  if (!oldPrice || oldPrice === 0) return 0;
  
  return ((recentPrice - oldPrice) / oldPrice) * 100;
});

// Methods
tokenSchema.methods.updatePrice = function(newPrice) {
  this.price = newPrice;
  this.priceHistory.push({
    price: newPrice,
    timestamp: new Date()
  });
  
  // Keep only last 100 price points
  if (this.priceHistory.length > 100) {
    this.priceHistory = this.priceHistory.slice(-100);
  }
  
  return this.save();
};

tokenSchema.methods.setAnalysisCompleted = function(data) {
  this.analysisStatus = 'completed';
  this.symbol = data.symbol || this.symbol;
  this.name = data.name || this.name;
  this.price = data.price || this.price;
  this.marketCap = data.marketCap || this.marketCap;
  this.volume24h = data.volume24h || this.volume24h;
  this.holders = data.holders || this.holders;
  this.liquidity = data.liquidity || this.liquidity;
  this.fdv = data.fdv || this.fdv;
  
  return this.save();
};

tokenSchema.methods.setAnalysisFailed = function(error) {
  this.analysisStatus = 'failed';
  this.analysisError = {
    message: error.message,
    code: error.code || 'UNKNOWN',
    timestamp: new Date()
  };
  
  return this.save();
};

tokenSchema.methods.incrementAnalysisAttempts = function() {
  this.analysisAttempts += 1;
  this.lastAnalysisAttempt = new Date();
  
  return this.save();
};

// Static methods
tokenSchema.statics.getBySource = function(sourceId, limit = 100, skip = 0) {
  return this.find({ sourceId })
    .sort({ discoveredAt: -1 })
    .limit(limit)
    .skip(skip)
    .populate('sourceId');
};

tokenSchema.statics.getPendingAnalysis = function(limit = 10) {
  return this.find({ 
    analysisStatus: 'pending',
    analysisAttempts: { $lt: 3 }
  })
    .sort({ discoveredAt: 1 })
    .limit(limit);
};

tokenSchema.statics.getAnalyticsData = function(sourceId, timeRange = '24h') {
  const timeRanges = {
    '1h': 1 * 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000
  };
  
  const since = new Date(Date.now() - timeRanges[timeRange]);
  
  return this.aggregate([
    {
      $match: {
        sourceId: mongoose.Types.ObjectId(sourceId),
        discoveredAt: { $gte: since }
      }
    },
    {
      $group: {
        _id: null,
        totalTokens: { $sum: 1 },
        analyzedTokens: {
          $sum: { $cond: [{ $eq: ['$analysisStatus', 'completed'] }, 1, 0] }
        },
        avgRiskScore: { $avg: '$riskScore' },
        avgPrice: { $avg: '$price' },
        avgHolders: { $avg: '$holders' }
      }
    }
  ]);
};

module.exports = mongoose.model('Token', tokenSchema);
