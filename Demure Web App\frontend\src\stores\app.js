import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/utils/api'

export const useAppStore = defineStore('app', () => {
  const loading = ref(false)
  const error = ref(null)
  const stats = ref({
    totalSources: 0,
    activeSources: 0,
    totalTokens: 0,
    analyzedTokens: 0,
    queueLength: 0
  })
  
  const loadInitialData = async () => {
    loading.value = true
    error.value = null
    
    try {
      // Load global stats
      await loadGlobalStats()
    } catch (err) {
      error.value = err.message
      console.error('Failed to load initial data:', err)
    } finally {
      loading.value = false
    }
  }
  
  const loadGlobalStats = async () => {
    try {
      const response = await api.get('/analytics/global')
      if (response.data.success) {
        const data = response.data.data
        stats.value = {
          totalSources: data.totalSources || 0,
          activeSources: data.activeSources || 0,
          totalTokens: data.globalStats?.totalTokens || 0,
          analyzedTokens: data.globalStats?.analyzedTokens || 0,
          queueLength: 0 // Will be updated via socket
        }
      }
    } catch (err) {
      console.error('Failed to load global stats:', err)
      throw err
    }
  }
  
  const updateStats = (newStats) => {
    stats.value = { ...stats.value, ...newStats }
  }
  
  const setLoading = (isLoading) => {
    loading.value = isLoading
  }
  
  const setError = (errorMessage) => {
    error.value = errorMessage
  }
  
  const clearError = () => {
    error.value = null
  }
  
  return {
    loading,
    error,
    stats,
    loadInitialData,
    loadGlobalStats,
    updateStats,
    setLoading,
    setError,
    clearError
  }
})
