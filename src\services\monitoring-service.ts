import { logger } from './logger-service'
import { RpcConnectionManager } from '../providers/solana'
import { WalletPool } from '../config/wallet-pool'
import fs from 'fs'
import path from 'path'
import chalk from 'chalk'
import cron from 'node-cron'

export interface SystemHealth {
  timestamp: Date
  rpcConnections: {
    total: number
    healthy: number
    failed: number
  }
  heliusConnections: {
    total: number
    available: number
    failed: number
  }
  wallets: {
    total: number
    active: number
    banned: number
  }
  memory: {
    used: number
    total: number
    percentage: number
  }
  uptime: number
  errors: {
    last24h: number
    lastHour: number
    critical: number
  }
}

export interface ErrorSummary {
  component: string
  errorCount: number
  lastError: Date
  errorTypes: Record<string, number>
  severity: 'low' | 'medium' | 'high' | 'critical'
}

/**
 * Monitoring Service
 *
 * Provides system health monitoring, error tracking, and performance metrics
 * for the wallet tracker system.
 */
export class MonitoringService {
  private static instance: MonitoringService
  private errorCounts: Map<string, number> = new Map()
  private lastErrors: Map<string, Date> = new Map()
  private criticalErrors: number = 0
  private startTime: Date = new Date()

  private constructor() {
    this.setupMonitoring()
  }

  public static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService()
    }
    return MonitoringService.instance
  }

  private setupMonitoring(): void {
    // Log system health every hour
    if (process.env.ENABLE_STATS_LOGGING === 'true') {
      cron.schedule('0 * * * *', () => {
        this.logHourlyHealth()
      })
    }

    // Generate daily reports
    if (process.env.ENABLE_DAILY_REPORTS === 'true') {
      cron.schedule('0 0 * * *', () => {
        this.generateDailyReport()
      })
    }

    // Monitor critical errors
    cron.schedule('*/5 * * * *', () => {
      this.checkCriticalErrors()
    })

    logger.info('Monitoring service initialized', {
      component: 'monitoring-service',
      metadata: {
        statsLogging: process.env.ENABLE_STATS_LOGGING === 'true',
        dailyReports: process.env.ENABLE_DAILY_REPORTS === 'true'
      }
    })
  }

  public getSystemHealth(): SystemHealth {
    const connectionStats = RpcConnectionManager.getConnectionStats()
    const memoryUsage = process.memoryUsage()
    const uptime = Date.now() - this.startTime.getTime()

    return {
      timestamp: new Date(),
      rpcConnections: {
        total: connectionStats.totalRpcEndpoints,
        healthy: connectionStats.totalRpcEndpoints, // Assume healthy for now
        failed: 0
      },
      heliusConnections: {
        total: connectionStats.totalHeliusKeys,
        available: connectionStats.availableHeliusKeys,
        failed: connectionStats.failedHeliusKeys
      },
      wallets: {
        total: WalletPool.subscriptions.size,
        active: WalletPool.subscriptions.size,
        banned: WalletPool.bannedWallets.size
      },
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
      },
      uptime: uptime,
      errors: {
        last24h: this.getErrorCount('24h'),
        lastHour: this.getErrorCount('1h'),
        critical: this.criticalErrors
      }
    }
  }

  public recordError(component: string, severity: 'low' | 'medium' | 'high' | 'critical'): void {
    const key = `${component}_${severity}`
    this.errorCounts.set(key, (this.errorCounts.get(key) || 0) + 1)
    this.lastErrors.set(component, new Date())

    if (severity === 'critical') {
      this.criticalErrors++
    }

    // Log error metrics
    logger.debug('Error recorded', {
      component: 'monitoring-service',
      metadata: { component, severity, totalErrors: this.errorCounts.get(key) }
    })
  }

  public getErrorSummary(): ErrorSummary[] {
    const summaries: ErrorSummary[] = []
    const components = new Set<string>()

    // Collect all components
    for (const [key] of this.errorCounts) {
      const component = key.split('_')[0]
      components.add(component)
    }

    // Generate summary for each component
    for (const component of components) {
      const errorTypes: Record<string, number> = {}
      let totalErrors = 0
      let maxSeverity: 'low' | 'medium' | 'high' | 'critical' = 'low'

      for (const [key, count] of this.errorCounts) {
        if (key.startsWith(component + '_')) {
          const severity = key.split('_')[1] as 'low' | 'medium' | 'high' | 'critical'
          errorTypes[severity] = count
          totalErrors += count

          // Determine max severity
          const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 }
          if (severityLevels[severity] > severityLevels[maxSeverity]) {
            maxSeverity = severity
          }
        }
      }

      summaries.push({
        component,
        errorCount: totalErrors,
        lastError: this.lastErrors.get(component) || new Date(0),
        errorTypes,
        severity: maxSeverity
      })
    }

    return summaries.sort((a, b) => b.errorCount - a.errorCount)
  }

  private getErrorCount(timeframe: '1h' | '24h'): number {
    // Simplified implementation - in production, you'd want to track timestamps
    const multiplier = timeframe === '1h' ? 0.1 : 1
    let total = 0
    for (const count of this.errorCounts.values()) {
      total += Math.floor(count * multiplier)
    }
    return total
  }

  private logHourlyHealth(): void {
    const health = this.getSystemHealth()

    logger.logHourlyStats({
      timestamp: health.timestamp.toISOString(),
      rpcConnections: health.rpcConnections,
      heliusConnections: health.heliusConnections,
      wallets: health.wallets,
      memoryUsagePercent: Math.round(health.memory.percentage),
      uptimeHours: Math.round(health.uptime / (1000 * 60 * 60)),
      errorsLastHour: health.errors.lastHour,
      criticalErrors: health.errors.critical
    })

    // Console summary
    console.log(chalk.cyanBright('\n📊 Hourly Health Check'))
    console.log(chalk.greenBright(`🔗 RPC: ${health.rpcConnections.healthy}/${health.rpcConnections.total} healthy`))
    console.log(chalk.blueBright(`🔑 Helius: ${health.heliusConnections.available}/${health.heliusConnections.total} available`))
    console.log(chalk.yellowBright(`👛 Wallets: ${health.wallets.active} active, ${health.wallets.banned} banned`))
    console.log(chalk.magentaBright(`💾 Memory: ${Math.round(health.memory.percentage)}% used`))
    console.log(chalk.redBright(`❌ Errors: ${health.errors.lastHour} last hour, ${health.errors.critical} critical`))
  }

  private async generateDailyReport(): Promise<void> {
    const health = this.getSystemHealth()
    const errorSummary = this.getErrorSummary()

    const report = {
      date: new Date().toISOString().split('T')[0],
      systemHealth: health,
      errorSummary,
      topErrors: errorSummary.slice(0, 5),
      recommendations: this.generateRecommendations(health, errorSummary)
    }

    logger.logDailyReport(report)

    // Save to file if file logging is enabled
    if (process.env.ENABLE_FILE_LOGGING === 'true') {
      const logDir = process.env.LOG_DIRECTORY || 'logs'
      const reportPath = path.join(logDir, `daily-report-${report.date}.json`)

      try {
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
        logger.info('Daily report saved', {
          component: 'monitoring-service',
          metadata: { reportPath }
        })
      } catch (error) {
        logger.error('Failed to save daily report', error as Error, {
          component: 'monitoring-service'
        })
      }
    }

    // Console report
    console.log(chalk.cyanBright('\n📈 Daily Report Generated'))
    console.log(chalk.greenBright(`📅 Date: ${report.date}`))
    console.log(chalk.yellowBright(`⏱️  Uptime: ${Math.round(health.uptime / (1000 * 60 * 60))} hours`))
    console.log(chalk.redBright(`❌ Total Errors: ${health.errors.last24h}`))
    console.log(chalk.magentaBright(`🚨 Critical Errors: ${health.errors.critical}`))
  }

  private generateRecommendations(health: SystemHealth, errors: ErrorSummary[]): string[] {
    const recommendations: string[] = []

    // Memory recommendations
    if (health.memory.percentage > 80) {
      recommendations.push('High memory usage detected. Consider restarting the service or reducing tracked wallets.')
    }

    // Helius key recommendations
    if (health.heliusConnections.failed > health.heliusConnections.total * 0.3) {
      recommendations.push('Many Helius API keys are failing. Check API key validity and rate limits.')
    }

    // Error recommendations
    const criticalComponents = errors.filter(e => e.severity === 'critical')
    if (criticalComponents.length > 0) {
      recommendations.push(`Critical errors in: ${criticalComponents.map(c => c.component).join(', ')}. Immediate attention required.`)
    }

    // Wallet recommendations
    if (health.wallets.banned > health.wallets.total * 0.1) {
      recommendations.push('High number of banned wallets. Review rate limiting settings.')
    }

    return recommendations
  }

  private checkCriticalErrors(): void {
    const health = this.getSystemHealth()

    // Check for critical conditions
    if (health.heliusConnections.available === 0) {
      logger.logCriticalError('All Helius API keys failed', new Error('No available Helius connections'), {
        component: 'helius-monitor'
      })
    }

    if (health.memory.percentage > 98) {
      logger.logCriticalError('Critical memory usage', new Error(`Memory usage: ${health.memory.percentage}%`), {
        component: 'memory-monitor'
      })
    }

    if (health.errors.lastHour > 100) {
      logger.logCriticalError('High error rate', new Error(`${health.errors.lastHour} errors in last hour`), {
        component: 'error-monitor'
      })
    }
  }

  public getHealthSummary(): string {
    const health = this.getSystemHealth()

    return `
🏥 <b>System Health Summary</b>

🔗 <b>RPC Connections:</b> ${health.rpcConnections.healthy}/${health.rpcConnections.total} healthy
🔑 <b>Helius Keys:</b> ${health.heliusConnections.available}/${health.heliusConnections.total} available
👛 <b>Wallets:</b> ${health.wallets.active} active, ${health.wallets.banned} banned
💾 <b>Memory:</b> ${Math.round(health.memory.percentage)}% used
⏱️ <b>Uptime:</b> ${Math.round(health.uptime / (1000 * 60 * 60))} hours
❌ <b>Errors:</b> ${health.errors.lastHour} last hour, ${health.errors.critical} critical

<i>Last updated: ${health.timestamp.toLocaleString()}</i>
    `
  }

  public exportLogs(hours: number = 24): string {
    const logDir = process.env.LOG_DIRECTORY || 'logs'
    const files = fs.readdirSync(logDir)
      .filter(file => file.endsWith('.log'))
      .sort()
      .reverse()
      .slice(0, 3) // Last 3 log files

    return `
📋 <b>Recent Log Files</b>

${files.map(file => `📄 ${file}`).join('\n')}

💡 <b>Log Directory:</b> <code>${path.resolve(logDir)}</code>
🔍 <b>View logs:</b> Check the files above for detailed error information
    `
  }
}

// Export singleton instance
export const monitoring = MonitoringService.getInstance()
