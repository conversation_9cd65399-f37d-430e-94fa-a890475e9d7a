import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: 'Dashboard',
      icon: 'Monitor'
    }
  },
  {
    path: '/sources',
    name: 'Sources',
    component: () => import('@/views/Sources.vue'),
    meta: {
      title: 'Sources Management',
      icon: 'Connection'
    }
  },
  {
    path: '/sources/:id',
    name: 'SourceDetail',
    component: () => import('@/views/SourceDetail.vue'),
    meta: {
      title: 'Source Details',
      icon: 'Connection'
    }
  },
  {
    path: '/tokens',
    name: 'Tokens',
    component: () => import('@/views/Tokens.vue'),
    meta: {
      title: 'Token Analysis',
      icon: 'Coin'
    }
  },
  {
    path: '/tokens/:id',
    name: 'TokenDetail',
    component: () => import('@/views/TokenDetail.vue'),
    meta: {
      title: 'Token Details',
      icon: 'Coin'
    }
  },
  {
    path: '/analytics',
    name: 'Analytics',
    component: () => import('@/views/Analytics.vue'),
    meta: {
      title: 'Analytics & Reports',
      icon: 'DataAnalysis'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    meta: {
      title: 'Settings',
      icon: 'Setting'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: 'Page Not Found'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach((to, from, next) => {
  // Set page title
  const title = to.meta.title || 'Demure Web App'
  document.title = `${title} - Token Analysis Platform`
  
  next()
})

export default router
