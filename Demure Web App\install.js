#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    🚀 DEMURE WEB APP                         ║
║              Multi-Source Token Analysis Platform            ║
║                     Installation Script                      ║
╚══════════════════════════════════════════════════════════════╝
`);

const steps = [
  'Checking Node.js version',
  'Installing backend dependencies',
  'Installing frontend dependencies', 
  'Setting up environment files',
  'Creating log directories',
  'Setting up database',
  'Building frontend',
  'Final setup'
];

let currentStep = 0;

function logStep(message) {
  currentStep++;
  console.log(`\n[${currentStep}/${steps.length}] ${message}...`);
}

function runCommand(command, cwd = process.cwd()) {
  try {
    execSync(command, { 
      cwd, 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'development' }
    });
    return true;
  } catch (error) {
    console.error(`❌ Failed to run: ${command}`);
    console.error(error.message);
    return false;
  }
}

function createDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ Created directory: ${dirPath}`);
  } else {
    console.log(`📁 Directory already exists: ${dirPath}`);
  }
}

function copyFile(src, dest) {
  if (fs.existsSync(src) && !fs.existsSync(dest)) {
    fs.copyFileSync(src, dest);
    console.log(`✅ Created: ${dest}`);
  } else if (!fs.existsSync(src)) {
    console.log(`⚠️  Source file not found: ${src}`);
  } else {
    console.log(`📄 File already exists: ${dest}`);
  }
}

async function main() {
  try {
    // Step 1: Check Node.js version
    logStep(steps[0]);
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 16) {
      console.error('❌ Node.js 16 or higher is required');
      console.error(`Current version: ${nodeVersion}`);
      process.exit(1);
    }
    console.log(`✅ Node.js version: ${nodeVersion}`);

    // Step 2: Install backend dependencies
    logStep(steps[1]);
    if (!runCommand('npm install')) {
      throw new Error('Failed to install backend dependencies');
    }

    // Step 3: Install frontend dependencies
    logStep(steps[2]);
    if (!runCommand('npm install', path.join(process.cwd(), 'frontend'))) {
      throw new Error('Failed to install frontend dependencies');
    }

    // Step 4: Setup environment files
    logStep(steps[3]);
    copyFile('.env.example', '.env');
    
    // Step 5: Create directories
    logStep(steps[4]);
    createDirectory('logs');
    createDirectory('exports');
    createDirectory('config');

    // Step 6: Database setup info
    logStep(steps[5]);
    console.log('📋 Database setup required:');
    console.log('   • Install MongoDB: https://docs.mongodb.com/manual/installation/');
    console.log('   • Install Redis: https://redis.io/download');
    console.log('   • Update MONGODB_URI and REDIS_URL in .env file');

    // Step 7: Build frontend
    logStep(steps[6]);
    if (!runCommand('npm run build', path.join(process.cwd(), 'frontend'))) {
      console.log('⚠️  Frontend build failed, but you can run in development mode');
    }

    // Step 8: Final setup
    logStep(steps[7]);
    console.log('✅ Installation completed successfully!');
    
    console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    🎉 INSTALLATION COMPLETE                  ║
╠══════════════════════════════════════════════════════════════╣
║  Next Steps:                                                 ║
║                                                              ║
║  1. Configure your .env file:                               ║
║     • Add your Telegram API credentials                     ║
║     • Set MongoDB and Redis URLs                            ║
║                                                              ║
║  2. Start the services:                                     ║
║     • MongoDB: mongod                                       ║
║     • Redis: redis-server                                   ║
║                                                              ║
║  3. Run the application:                                    ║
║     • Development: npm run dev                              ║
║     • Production: npm start                                 ║
║                                                              ║
║  4. Access the dashboard:                                   ║
║     • Backend: http://localhost:3002                       ║
║     • Frontend: http://localhost:3000 (dev mode)           ║
║                                                              ║
║  📚 Documentation: README.md                                ║
║  🆘 Support: Create an issue on GitHub                      ║
╚══════════════════════════════════════════════════════════════╝
    `);

  } catch (error) {
    console.error('\n❌ Installation failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   • Check Node.js version (16+ required)');
    console.error('   • Ensure npm is working properly');
    console.error('   • Check network connectivity');
    console.error('   • Try running: npm cache clean --force');
    process.exit(1);
  }
}

main();
