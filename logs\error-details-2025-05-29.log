{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 94.12904501934678%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 16:35:00"}
{"code":"CRITICAL_ERROR","component":"process","context":{"component":"process","metadata":{"promise":"[object Promise]"}},"level":"error","message":"Error tracked Unhandled Promise Rejection","severity":"critical","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\src\\repositories\\prisma\\user.ts:30:36\n\n  27 }\n  28 \n  29 public async getById(userId: string) {\n→ 30   const user = await prisma.user.findUnique(\nerror: Error validating datasource `db`: the URL must start with the protocol `file:`.\n  -->  schema.prisma:7\n   | \n 6 |   provider = \"sqlite\"\n 7 |   url      = env(\"DATABASE_URL\")\n   | \n\nValidation Error Count: 1\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@prisma\\client\\runtime\\library.js:121:7615)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)","timestamp":"2025-05-29 16:36:58"}
{"code":"CRITICAL_ERROR","component":"process","context":{"component":"process","metadata":{"promise":"[object Promise]"}},"level":"error","message":"Error tracked Unhandled Promise Rejection","severity":"critical","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\src\\repositories\\prisma\\user.ts:30:36\n\n  27 }\n  28 \n  29 public async getById(userId: string) {\n→ 30   const user = await prisma.user.findUnique(\nError in connector: Error creating a database connection. (Kind: An error occurred during DNS resolution: no record found for Query { name: Name(\"_mongodb._tcp.cluster0.s8spl.mongodb.net.\"), query_type: SRV, query_class: IN }, labels: {})\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@prisma\\client\\runtime\\library.js:121:7615)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)","timestamp":"2025-05-29 16:39:34"}
{"code":"CRITICAL_ERROR","component":"memory-monitor","context":{"component":"memory-monitor"},"level":"error","message":"Error tracked Critical memory usage","severity":"critical","stack":"Error: Memory usage: 96.44838549546142%\n    at MonitoringService.checkCriticalErrors (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\src\\services\\monitoring-service.ts:305:56)\n    at Task._execution (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\src\\services\\monitoring-service.ts:88:12)\n    at Task.execute (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-cron\\src\\task.js:17:25)\n    at ScheduledTask.now (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-cron\\src\\scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-cron\\src\\scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:518:28)\n    at Scheduler.emit (node:domain:489:12)\n    at Timeout.matchTime [as _onTimeout] (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-cron\\src\\scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)","timestamp":"2025-05-29 16:40:00"}
{"code":"CRITICAL_ERROR","component":"process","context":{"component":"process","metadata":{"promise":"[object Promise]"}},"level":"error","message":"Error tracked Unhandled Promise Rejection","severity":"critical","stack":"Error: ETELEGRAM: 400 Bad Request: MESSAGE_TOO_LONG\n    at C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-telegram-bot-api\\src\\telegram.js:316:15\n    at tryCatcher (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\util.js:16:23)\n    at Promise._settlePromiseFromHandler (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\promise.js:547:31)\n    at Promise._settlePromise (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\promise.js:604:18)\n    at Promise._settlePromise0 (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\promise.js:649:10)\n    at Promise._settlePromises (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\promise.js:729:18)\n    at _drainQueueStep (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\async.js:93:12)\n    at _drainQueue (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\async.js:86:9)\n    at Async._drainQueues (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\async.js:102:5)\n    at Immediate.Async.drainQueues (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\async.js:15:14)\n    at processImmediate (node:internal/timers:485:21)","timestamp":"2025-05-29 16:54:39"}
{"code":"CRITICAL_ERROR","component":"process","context":{"component":"process","metadata":{"promise":"[object Promise]"}},"level":"error","message":"Error tracked Unhandled Promise Rejection","severity":"critical","stack":"Error: ETELEGRAM: 400 Bad Request: MESSAGE_TOO_LONG\n    at C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\node-telegram-bot-api\\src\\telegram.js:316:15\n    at tryCatcher (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\util.js:16:23)\n    at Promise._settlePromiseFromHandler (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\promise.js:547:31)\n    at Promise._settlePromise (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\promise.js:604:18)\n    at Promise._settlePromise0 (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\promise.js:649:10)\n    at Promise._settlePromises (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\promise.js:729:18)\n    at _drainQueueStep (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\async.js:93:12)\n    at _drainQueue (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\async.js:86:9)\n    at Async._drainQueues (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\async.js:102:5)\n    at Immediate.Async.drainQueues (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\bluebird\\js\\release\\async.js:15:14)\n    at processImmediate (node:internal/timers:485:21)","timestamp":"2025-05-29 16:54:58"}
