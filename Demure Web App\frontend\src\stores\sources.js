import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useSourcesStore = defineStore('sources', () => {
  const sources = ref([])
  const loading = ref(false)
  const error = ref(null)
  const telegramAccounts = ref([])
  
  // Computed
  const activeSources = computed(() => 
    sources.value.filter(source => source.isActive)
  )
  
  const sourcesByType = computed(() => {
    const grouped = {}
    sources.value.forEach(source => {
      if (!grouped[source.type]) {
        grouped[source.type] = []
      }
      grouped[source.type].push(source)
    })
    return grouped
  })
  
  // Actions
  const loadSources = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.get('/sources')
      if (response.data.success) {
        sources.value = response.data.data
      }
    } catch (err) {
      error.value = err.message
      console.error('Failed to load sources:', err)
    } finally {
      loading.value = false
    }
  }
  
  const loadTelegramAccounts = async () => {
    try {
      const response = await api.get('/sources/telegram/accounts')
      if (response.data.success) {
        telegramAccounts.value = response.data.data.accounts
      }
    } catch (err) {
      console.error('Failed to load Telegram accounts:', err)
    }
  }
  
  const createSource = async (sourceData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/sources', sourceData)
      if (response.data.success) {
        sources.value.push(response.data.data)
        return response.data.data
      }
    } catch (err) {
      error.value = err.response?.data?.error || err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const updateSource = async (sourceId, updates) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.put(`/sources/${sourceId}`, updates)
      if (response.data.success) {
        const index = sources.value.findIndex(s => s._id === sourceId)
        if (index > -1) {
          sources.value[index] = response.data.data
        }
        return response.data.data
      }
    } catch (err) {
      error.value = err.response?.data?.error || err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const toggleSource = async (sourceId) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.patch(`/sources/${sourceId}/toggle`)
      if (response.data.success) {
        const index = sources.value.findIndex(s => s._id === sourceId)
        if (index > -1) {
          sources.value[index] = response.data.data
        }
        return response.data.data
      }
    } catch (err) {
      error.value = err.response?.data?.error || err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const deleteSource = async (sourceId) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.delete(`/sources/${sourceId}`)
      if (response.data.success) {
        sources.value = sources.value.filter(s => s._id !== sourceId)
        return response.data.data
      }
    } catch (err) {
      error.value = err.response?.data?.error || err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const getSourceById = (sourceId) => {
    return sources.value.find(s => s._id === sourceId)
  }
  
  const getSourceStats = async (sourceId, timeRange = '24h') => {
    try {
      const response = await api.get(`/sources/${sourceId}/stats`, {
        params: { timeRange }
      })
      if (response.data.success) {
        return response.data.data
      }
    } catch (err) {
      console.error('Failed to get source stats:', err)
      throw err
    }
  }
  
  const addSource = (source) => {
    sources.value.push(source)
  }
  
  const updateSourceInList = (updatedSource) => {
    const index = sources.value.findIndex(s => s._id === updatedSource._id)
    if (index > -1) {
      sources.value[index] = updatedSource
    }
  }
  
  const removeSourceFromList = (sourceId) => {
    sources.value = sources.value.filter(s => s._id !== sourceId)
  }
  
  const clearError = () => {
    error.value = null
  }
  
  return {
    // State
    sources,
    loading,
    error,
    telegramAccounts,
    
    // Computed
    activeSources,
    sourcesByType,
    
    // Actions
    loadSources,
    loadTelegramAccounts,
    createSource,
    updateSource,
    toggleSource,
    deleteSource,
    getSourceById,
    getSourceStats,
    addSource,
    updateSourceInList,
    removeSourceFromList,
    clearError
  }
})
