<template>
  <div class="dashboard">
    <!-- Header -->
    <div class="dashboard-header">
      <h1>Dashboard</h1>
      <p class="subtitle">Multi-Source Token Analysis Platform Overview</p>
    </div>
    
    <!-- Stats Cards -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stat-content">
            <div class="stat-icon sources">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalSources }}</div>
              <div class="stat-label">Total Sources</div>
              <div class="stat-sublabel">{{ stats.activeSources }} active</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stat-content">
            <div class="stat-icon tokens">
              <el-icon><Coin /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalTokens }}</div>
              <div class="stat-label">Tokens Discovered</div>
              <div class="stat-sublabel">{{ stats.analyzedTokens }} analyzed</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stat-content">
            <div class="stat-icon queue">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.queueLength }}</div>
              <div class="stat-label">Analysis Queue</div>
              <div class="stat-sublabel">{{ processingRate }}/min</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ successRate }}%</div>
              <div class="stat-label">Success Rate</div>
              <div class="stat-sublabel">Last 24h</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- Charts and Activity -->
    <el-row :gutter="20" class="content-row">
      <!-- Activity Chart -->
      <el-col :xs="24" :lg="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>Token Discovery Activity</span>
              <el-select v-model="chartTimeRange" size="small" style="width: 120px">
                <el-option label="1 Hour" value="1h" />
                <el-option label="24 Hours" value="24h" />
                <el-option label="7 Days" value="7d" />
                <el-option label="30 Days" value="30d" />
              </el-select>
            </div>
          </template>
          
          <div class="chart-container">
            <canvas ref="activityChart"></canvas>
          </div>
        </el-card>
      </el-col>
      
      <!-- Recent Activity -->
      <el-col :xs="24" :lg="8">
        <el-card class="activity-card">
          <template #header>
            <span>Recent Activity</span>
          </template>
          
          <div class="activity-list">
            <div 
              v-for="activity in recentActivity" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon :class="getActivityIconClass(activity.type)">
                  <component :is="getActivityIcon(activity.type)" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
              </div>
            </div>
            
            <div v-if="recentActivity.length === 0" class="no-activity">
              <el-icon><InfoFilled /></el-icon>
              <span>No recent activity</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- Sources Overview -->
    <el-row :gutter="20" class="content-row">
      <el-col :span="24">
        <el-card class="sources-overview">
          <template #header>
            <div class="card-header">
              <span>Sources Overview</span>
              <el-button type="primary" size="small" @click="$router.push('/sources')">
                Manage Sources
              </el-button>
            </div>
          </template>
          
          <el-table :data="sourcesData" style="width: 100%">
            <el-table-column prop="name" label="Source Name" min-width="150" />
            <el-table-column prop="type" label="Type" width="100">
              <template #default="{ row }">
                <el-tag :type="getSourceTypeColor(row.type)">
                  {{ row.type.toUpperCase() }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="Status" width="120">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="tokensFound" label="Tokens Found" width="120" />
            <el-table-column prop="tokensAnalyzed" label="Analyzed" width="120" />
            <el-table-column prop="successRate" label="Success Rate" width="120">
              <template #default="{ row }">
                {{ row.successRate }}%
              </template>
            </el-table-column>
            <el-table-column prop="lastActivity" label="Last Activity" width="150">
              <template #default="{ row }">
                {{ formatTime(row.lastActivity) }}
              </template>
            </el-table-column>
            <el-table-column label="Actions" width="100">
              <template #default="{ row }">
                <el-button 
                  type="text" 
                  size="small"
                  @click="$router.push(`/sources/${row._id}`)"
                >
                  View
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { useSourcesStore } from '@/stores/sources'
import { useSocketStore } from '@/stores/socket'
import moment from 'moment'

export default {
  name: 'Dashboard',
  setup() {
    const appStore = useAppStore()
    const sourcesStore = useSourcesStore()
    const socketStore = useSocketStore()
    
    const chartTimeRange = ref('24h')
    const recentActivity = ref([])
    const processingRate = ref(0)
    
    // Computed
    const stats = computed(() => appStore.stats)
    
    const successRate = computed(() => {
      if (stats.value.totalTokens === 0) return 0
      return Math.round((stats.value.analyzedTokens / stats.value.totalTokens) * 100)
    })
    
    const sourcesData = computed(() => {
      return sourcesStore.sources.map(source => ({
        ...source,
        tokensFound: source.stats?.tokensFound || 0,
        tokensAnalyzed: source.stats?.tokensAnalyzed || 0,
        successRate: source.successRate || 0,
        lastActivity: source.stats?.lastActivity
      }))
    })
    
    // Methods
    const getActivityIcon = (type) => {
      const icons = {
        'token-discovered': 'Coin',
        'token-analyzed': 'SuccessFilled',
        'source-added': 'Plus',
        'source-error': 'WarningFilled'
      }
      return icons[type] || 'InfoFilled'
    }
    
    const getActivityIconClass = (type) => {
      const classes = {
        'token-discovered': 'text-info',
        'token-analyzed': 'text-success',
        'source-added': 'text-primary',
        'source-error': 'text-danger'
      }
      return classes[type] || 'text-info'
    }
    
    const getSourceTypeColor = (type) => {
      const colors = {
        'bot': 'primary',
        'channel': 'success',
        'group': 'warning'
      }
      return colors[type] || 'info'
    }
    
    const getStatusColor = (status) => {
      const colors = {
        'active': 'success',
        'paused': 'warning',
        'error': 'danger',
        'connecting': 'info'
      }
      return colors[status] || 'info'
    }
    
    const formatTime = (timestamp) => {
      if (!timestamp) return 'Never'
      return moment(timestamp).fromNow()
    }
    
    const loadDashboardData = async () => {
      try {
        await Promise.all([
          appStore.loadGlobalStats(),
          sourcesStore.loadSources()
        ])
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
      }
    }
    
    const setupSocketListeners = () => {
      socketStore.on('token-discovered', (data) => {
        recentActivity.value.unshift({
          id: Date.now(),
          type: 'token-discovered',
          title: 'New Token Discovered',
          description: `${data.address.substring(0, 8)}...${data.address.substring(data.address.length - 4)}`,
          timestamp: new Date()
        })
        
        // Keep only last 10 activities
        if (recentActivity.value.length > 10) {
          recentActivity.value = recentActivity.value.slice(0, 10)
        }
        
        // Update stats
        appStore.updateStats({
          totalTokens: stats.value.totalTokens + 1
        })
      })
      
      socketStore.on('token-analyzed', (data) => {
        if (data.status === 'completed') {
          recentActivity.value.unshift({
            id: Date.now(),
            type: 'token-analyzed',
            title: 'Token Analysis Complete',
            description: `${data.data?.symbol || 'Unknown'} - ${data.address.substring(0, 8)}...`,
            timestamp: new Date()
          })
          
          appStore.updateStats({
            analyzedTokens: stats.value.analyzedTokens + 1
          })
        }
      })
      
      socketStore.on('source-added', (data) => {
        recentActivity.value.unshift({
          id: Date.now(),
          type: 'source-added',
          title: 'New Source Added',
          description: `${data.name} (${data.type})`,
          timestamp: new Date()
        })
        
        sourcesStore.addSource(data)
      })
      
      socketStore.on('queue-updated', (data) => {
        appStore.updateStats({
          queueLength: data.queueLength
        })
      })
    }
    
    onMounted(async () => {
      await loadDashboardData()
      setupSocketListeners()
    })
    
    onUnmounted(() => {
      // Clean up socket listeners if needed
    })
    
    return {
      stats,
      chartTimeRange,
      recentActivity,
      processingRate,
      successRate,
      sourcesData,
      getActivityIcon,
      getActivityIconClass,
      getSourceTypeColor,
      getStatusColor,
      formatTime
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--text-secondary);
  font-size: 16px;
  margin: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.sources {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tokens {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.queue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-regular);
  margin-bottom: 2px;
}

.stat-sublabel {
  font-size: 12px;
  color: var(--text-secondary);
}

.content-row {
  margin-bottom: 24px;
}

.chart-card,
.activity-card,
.sources-overview {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
  position: relative;
}

.activity-list {
  height: 320px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  background: #f5f7fa;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.activity-description {
  font-size: 13px;
  color: var(--text-regular);
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.no-activity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
}

.no-activity .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

@media (max-width: 768px) {
  .stats-row .el-col {
    margin-bottom: 16px;
  }
  
  .content-row .el-col {
    margin-bottom: 16px;
  }
  
  .chart-card,
  .activity-card {
    height: auto;
  }
  
  .chart-container {
    height: 250px;
  }
  
  .activity-list {
    height: 250px;
  }
}
</style>
