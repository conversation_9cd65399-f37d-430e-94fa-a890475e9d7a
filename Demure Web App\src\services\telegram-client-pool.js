const { Telegram<PERSON>pi, StringSession, NewMessage } = require('telegram');
const Logger = require('../utils/logger');

class TelegramClientPool {
  constructor() {
    this.clients = new Map();
    this.accounts = this.loadAccounts();
    this.logger = new Logger();
    this.currentClientIndex = 0;
  }

  loadAccounts() {
    const accounts = [];
    let index = 1;

    // Load accounts from environment variables
    while (process.env[`TELEGRAM_API_ID_${index}`]) {
      accounts.push({
        id: `account_${index}`,
        apiId: parseInt(process.env[`TELEGRAM_API_ID_${index}`]),
        apiHash: process.env[`TELEGRAM_API_HASH_${index}`],
        phoneNumber: process.env[`TELEGRAM_PHONE_${index}`],
        session: process.env[`TELEGRAM_SESSION_${index}`] || ''
      });
      index++;
    }

    if (accounts.length === 0) {
      this.logger.warn('No Telegram accounts configured. Please set TELEGRAM_API_ID_1, TELEGRAM_API_HASH_1, etc.');
    }

    return accounts;
  }

  async initialize() {
    this.logger.info(`Initializing ${this.accounts.length} Telegram accounts...`);

    for (const account of this.accounts) {
      try {
        await this.initializeClient(account);
      } catch (error) {
        this.logger.error(`Failed to initialize account ${account.id}:`, error);
      }
    }

    this.logger.info(`Successfully initialized ${this.clients.size} Telegram clients`);
  }

  async initializeClient(account) {
    try {
      const session = new StringSession(account.session);
      const client = new TelegramApi(session, account.apiId, account.apiHash, {
        connectionRetries: 5,
        retryDelay: 1000,
        timeout: 10000
      });

      // Start the client
      await client.start({
        phoneNumber: async () => account.phoneNumber,
        password: async () => {
          // If 2FA is enabled, you'll need to handle this
          return '';
        },
        phoneCode: async () => {
          // For production, you'd need a proper way to handle this
          this.logger.warn(`Phone code required for ${account.phoneNumber}`);
          return '';
        },
        onError: (err) => {
          this.logger.error(`Auth error for ${account.id}:`, err);
        },
      });

      // Store the client
      this.clients.set(account.id, {
        client,
        account,
        isActive: true,
        lastUsed: new Date(),
        requestCount: 0,
        errorCount: 0
      });

      this.logger.info(`Client ${account.id} initialized successfully`);

      // Save session string for future use
      if (!account.session) {
        const sessionString = client.session.save();
        this.logger.info(`Session string for ${account.id}: ${sessionString}`);
      }

    } catch (error) {
      this.logger.error(`Failed to initialize client ${account.id}:`, error);
      throw error;
    }
  }

  getClient(accountId) {
    const clientData = this.clients.get(accountId);
    if (!clientData || !clientData.isActive) {
      throw new Error(`Client ${accountId} not available`);
    }

    clientData.lastUsed = new Date();
    clientData.requestCount++;

    return clientData.client;
  }

  getAvailableClient() {
    const availableClients = Array.from(this.clients.values())
      .filter(clientData => clientData.isActive)
      .sort((a, b) => a.requestCount - b.requestCount);

    if (availableClients.length === 0) {
      throw new Error('No available Telegram clients');
    }

    const clientData = availableClients[0];
    clientData.lastUsed = new Date();
    clientData.requestCount++;

    return clientData.client;
  }

  getNextClient() {
    const clientIds = Array.from(this.clients.keys()).filter(id =>
      this.clients.get(id).isActive
    );

    if (clientIds.length === 0) {
      throw new Error('No available Telegram clients');
    }

    const clientId = clientIds[this.currentClientIndex % clientIds.length];
    this.currentClientIndex++;

    return this.getClient(clientId);
  }

  async sendMessage(target, message, accountId = null) {
    try {
      const client = accountId ? this.getClient(accountId) : this.getAvailableClient();

      // Send message
      const result = await client.sendMessage(target, { message });

      this.logger.info(`Message sent to ${target}`, {
        accountId: accountId || 'auto-selected',
        messageLength: message.length
      });

      return result;
    } catch (error) {
      this.logger.error(`Failed to send message to ${target}:`, error);

      // Mark client as having an error
      if (accountId) {
        const clientData = this.clients.get(accountId);
        if (clientData) {
          clientData.errorCount++;
        }
      }

      throw error;
    }
  }

  async addEventHandler(handler, accountId = null) {
    try {
      if (accountId) {
        const client = this.getClient(accountId);
        client.addEventHandler(handler);
      } else {
        // Add handler to all clients
        for (const [id, clientData] of this.clients) {
          if (clientData.isActive) {
            clientData.client.addEventHandler(handler);
          }
        }
      }
    } catch (error) {
      this.logger.error('Failed to add event handler:', error);
      throw error;
    }
  }

  getStatus() {
    const status = {
      totalAccounts: this.accounts.length,
      activeClients: 0,
      totalRequests: 0,
      totalErrors: 0,
      clients: {}
    };

    for (const [id, clientData] of this.clients) {
      if (clientData.isActive) {
        status.activeClients++;
      }

      status.totalRequests += clientData.requestCount;
      status.totalErrors += clientData.errorCount;

      status.clients[id] = {
        isActive: clientData.isActive,
        lastUsed: clientData.lastUsed,
        requestCount: clientData.requestCount,
        errorCount: clientData.errorCount,
        phoneNumber: clientData.account.phoneNumber
      };
    }

    return status;
  }

  getClientCount() {
    return this.clients.size;
  }

  async disconnect() {
    this.logger.info('Disconnecting all Telegram clients...');

    for (const [id, clientData] of this.clients) {
      try {
        await clientData.client.disconnect();
        this.logger.info(`Client ${id} disconnected`);
      } catch (error) {
        this.logger.error(`Error disconnecting client ${id}:`, error);
      }
    }

    this.clients.clear();
  }

  // Rate limiting helpers
  async waitForRateLimit(accountId = null) {
    const delay = parseInt(process.env.SOUL_SCANNER_DELAY) || 2000;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  isRateLimited(accountId) {
    const clientData = this.clients.get(accountId);
    if (!clientData) return false;

    const now = Date.now();
    const lastUsed = clientData.lastUsed.getTime();
    const minDelay = parseInt(process.env.SOUL_SCANNER_DELAY) || 2000;

    return (now - lastUsed) < minDelay;
  }
}

module.exports = TelegramClientPool;
