import axios from 'axios'
import { ElMessage } from 'element-plus'

// Create axios instance
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const message = error.response?.data?.error || error.message || 'An error occurred'
    
    // Don't show error messages for certain endpoints
    const silentEndpoints = ['/health', '/stats']
    const isSilent = silentEndpoints.some(endpoint => 
      error.config?.url?.includes(endpoint)
    )
    
    if (!isSilent) {
      ElMessage.error(message)
    }
    
    // Handle specific error codes
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login if implemented
      localStorage.removeItem('auth_token')
      // window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)

export default api
