2025-05-29 17:10:46 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"kWsFyJrA6e28zTLHKxQjEG31ChVJaeA6GMzYccae2MKeBjukCMcyejha8SJyJqQuRfgMXwqSL46z8qD8MDooQ8Y","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1c2dfd63-5446-4923-b00e-78af18fa3e4b\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1c2dfd63-5446-4923-b00e-78af18fa3e4b\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:46 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"RcH6objy69EwUKNm7XdWRwJtFHmb2iMuyibZ3668SRUq7bVknpbXR59qLynMmLMnjwXF7WaNJHhyxK6UyLcmrjj","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"755af850-479c-488a-b27f-424f97edff77\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"755af850-479c-488a-b27f-424f97edff77\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:50 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3YNTEpt4DX1MV2kMXzNCX9NeCEAepuNizDkV67mdWedL5r41brxsjmtkXm4iFiX8sxrAiyY8HwG37KvCa1mSPUri","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"64e9624c-8be6-484b-ae59-9b499608a1d8\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"64e9624c-8be6-484b-ae59-9b499608a1d8\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:51 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3ESFnZe8WSrxm1wLrGorKQLXM2qb7b2GLDgev919sSkrTrfCZvnqa9yErE3h37UMS5wG735f38nvfhASnXxFJz1W","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b9e8ceea-e73e-4bc6-8e73-d0077de685c1\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"b9e8ceea-e73e-4bc6-8e73-d0077de685c1\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:51 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3XNUugTzQ3ydqBKdA4B23UM5LvvEe9ggHAtKTu3PkMUtYe8keBwo2T2AU65EsB92DGAkQQXMizUZYS68REDkrM1U","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c62f696e-94ab-49a0-adfd-fa533549d3da\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"c62f696e-94ab-49a0-adfd-fa533549d3da\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:52 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"44VHFYGhRNxEsD57jfCRjCzQLPtQNV1vmMu66Z4FyBe6hmmwAMNXDCj57SDJ3bCzWyTREQUjcRFxHTr1TAK3D8eb","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0a4b0d96-c6f7-4950-939f-c463776bb3af\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"0a4b0d96-c6f7-4950-939f-c463776bb3af\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2XVZb9BhFM6XTEMKtoeqU6rjmYC7d8vDngdgSXSVeYdY6GJhFwByLLYF62f2Z8pX84ecWF79nRBC3uPXAjxeEb3M","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8c0d1482-dfb6-49c3-89f7-cc20aef98eab\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"8c0d1482-dfb6-49c3-89f7-cc20aef98eab\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:55 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5y3xNnJiAXaHCRG6erhXcmNUyaawWjhEJvHJvGLZ9JmaJGm3RzqtfGbxKJCoUwkB6VoxhTm5w84JN91wqwWzJy6t","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"3554d4cb-5bcb-4baf-8929-4ed71e2d0f90\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"3554d4cb-5bcb-4baf-8929-4ed71e2d0f90\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:56 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5n4ZEJmZCFPdvBRuA1of5UKtY94rvnRUtUUn4FWhz9NRU6UYn8izsDv84t7fbDQqR1hmuRfTNTeSVaWBxtcnvq1K","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d194f1d4-8a3a-42a8-951b-c9ab2e64960d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d194f1d4-8a3a-42a8-951b-c9ab2e64960d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:56 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"Vu6c53J8FEpBGDaWxbTj8oXNdUESDEZhTPPZDLmn83vvRT2wrw7ZJLgBEcRzKpDgBjtzdLquNPXjgcDN2k5cQLj","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e611886d-3e49-432f-91ba-981cf99661f7\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"e611886d-3e49-432f-91ba-981cf99661f7\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3tuA6bwMyXvftUJ8qFrb6TCohWR3KPwXUbxQ3viSnh4MoM1sP7vAzS3fEaFBSBXMS5JVcgoEwrrhyF7x9tGLCbAp","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"979b0faf-0361-4e05-be95-4b2beb307324\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"979b0faf-0361-4e05-be95-4b2beb307324\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:57 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3qUWoeVPhRXFjM1j51WTnfvuJpBaDWX7Ne9W1euwRJwbVeUXyxXChANmmeKnDkxmD3L4PZdbX4EHb7Ah1Ki1HYTb","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"324a8ea2-cd6c-4314-ad23-ea3e001f4e08\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"324a8ea2-cd6c-4314-ad23-ea3e001f4e08\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:10:59 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"kWsFyJrA6e28zTLHKxQjEG31ChVJaeA6GMzYccae2MKeBjukCMcyejha8SJyJqQuRfgMXwqSL46z8qD8MDooQ8Y","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9c8812c2-6047-4c67-8690-45a77c41c48d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"9c8812c2-6047-4c67-8690-45a77c41c48d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:00 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"RcH6objy69EwUKNm7XdWRwJtFHmb2iMuyibZ3668SRUq7bVknpbXR59qLynMmLMnjwXF7WaNJHhyxK6UyLcmrjj","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"25e5016e-6661-44a9-a9af-1de2e9bb2905\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"25e5016e-6661-44a9-a9af-1de2e9bb2905\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:02 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"eB93rVT8qKpM5VJotkCjkr1PyNWaapJvUAghVnSZvTy6HkA8k2cv2Rdg285K2s3Hu3cBat6En8KNrtsUZvLmSXG","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"6d6db689-4a2f-4a66-904a-39127e7000bc\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"6d6db689-4a2f-4a66-904a-39127e7000bc\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"5VEQ66m8LzQ8vrb27Vo11Kcc3i83LopSmsfXfvEGCjhwvUp6yDVoHBYBNCNttRgEnbpiwJtNZEyN3FgLXNuo7SXh","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1eaeebda-c9d5-4567-809f-074dd6d7c7ff\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"1eaeebda-c9d5-4567-809f-074dd6d7c7ff\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4rW7wTLgcTe89XHHvfPVY2Fun9uhshrDpaUvaTz1Mkk5VdNtaKSgv8b6d3UA37HpjkMFoWNSTz8tt85ScftEKrjf","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d16b2e4a-9e92-4a0f-bf06-2a4339e87007\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"d16b2e4a-9e92-4a0f-bf06-2a4339e87007\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"4Pr5oDyxEtV1EvrppSPQ86rS7sgXp4xKKWYkLfAbw5qtz6Q3LkY5Loo3zpUYQNnrMAYCJ4trRaUDDCmvsQw64X7i","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"35aff0ef-9a76-4135-bbe0-7bb976026ca2\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"35aff0ef-9a76-4135-bbe0-7bb976026ca2\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2mtJCf3fmn9JFJqeTPAdFx5uLd3YjcUZ37wScy2K8riBCbMdyjBUAyg72p3DYiE3hFnpAVghoBQV11TwaNndp9oC","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"72a9131a-d9fb-486a-9e76-7e8b19752f62\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"72a9131a-d9fb-486a-9e76-7e8b19752f62\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"38odVkGq8xCd72aT5soaADvDe7pyCtkdcpmRAneTcKxobZKqxNZxi1AJJBWmSortmxWXvPehbEPCdQpigyGwkeXF","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"820fea5e-a332-42aa-87b5-1b65fc261fc4\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"820fea5e-a332-42aa-87b5-1b65fc261fc4\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"he5wBEVderXVHXmpa34xrm9DBbNPxBxUS6J32vKMeTQBbhB9eeEFAqK1r7jRVcywezQCBo86nfchxDER9ykqu7q","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"76e35f5f-4e73-4cc9-99cd-2040515bfda9\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"76e35f5f-4e73-4cc9-99cd-2040515bfda9\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:03 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"2qEAr8tQZWG8egzZKHgGmoQSinHBtcxFvZXpc2kfZhFtVoaRCfyeifHC183TXG1bg5bBN8GqqR4rDiXTmYDu9XEZ","metadata":{"attempt":1,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"847d2950-8ccb-4fe9-b405-3f3af3d3f8ff\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"847d2950-8ccb-4fe9-b405-3f3af3d3f8ff\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
2025-05-29 17:11:04 ERROR [transaction-fetcher] Error fetching transaction details {"transactionSignature":"3YNTEpt4DX1MV2kMXzNCX9NeCEAepuNizDkV67mdWedL5r41brxsjmtkXm4iFiX8sxrAiyY8HwG37KvCa1mSPUri","metadata":{"attempt":2,"retries":4},"error":{"message":"429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"476750b6-d1ac-4568-bd1a-3c0ee4dc578d\" } ]\r\n","stack":"Error: 429 Too Many Requests: [ {\"jsonrpc\":\"2.0\",\"error\":{\"code\": 429, \"message\":\"Too many requests for a specific RPC call\"}, \"id\": \"476750b6-d1ac-4568-bd1a-3c0ee4dc578d\" } ]\r\n\n    at ClientBrowser.callServer (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main3\\node_modules\\@solana\\web3.js\\src\\connection.ts:1699:18)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","name":"Error"}}
