const Token = require('../models/Token');
const Source = require('../models/Source');
const Logger = require('../utils/logger');

class TokenAnalyzer {
  constructor(telegramPool, io) {
    this.telegramPool = telegramPool;
    this.io = io;
    this.logger = new Logger();
    this.processingQueue = [];
    this.isProcessing = false;
    this.soulScannerBot = process.env.SOUL_SCANNER_BOT_USERNAME || '@soul_scanner_bot';
    this.responseTimeout = parseInt(process.env.SOUL_SCANNER_TIMEOUT) || 30000;
    
    // Start processing queue
    this.startQueueProcessor();
    
    // Listen for new tokens
    this.setupTokenListener();
  }

  setupTokenListener() {
    this.io.on('connection', (socket) => {
      socket.on('token-discovered', (data) => {
        this.queueTokenForAnalysis(data.tokenId);
      });
    });
  }

  async queueTokenForAnalysis(tokenId) {
    try {
      const token = await Token.findById(tokenId);
      if (!token) {
        this.logger.warn(`Token ${tokenId} not found for analysis`);
        return;
      }

      if (token.analysisStatus !== 'pending') {
        this.logger.debug(`Token ${token.address} already processed or processing`);
        return;
      }

      this.processingQueue.push({
        tokenId: token._id,
        address: token.address,
        sourceId: token.sourceId,
        queuedAt: new Date()
      });

      this.logger.info(`Token ${token.address} queued for analysis`, {
        queueLength: this.processingQueue.length
      });

      // Emit queue update
      this.io.emit('queue-updated', {
        queueLength: this.processingQueue.length,
        token: {
          id: token._id,
          address: token.address
        }
      });

    } catch (error) {
      this.logger.error('Error queuing token for analysis:', error);
    }
  }

  async startQueueProcessor() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    this.logger.info('Starting token analysis queue processor');

    while (true) {
      try {
        if (this.processingQueue.length === 0) {
          await this.delay(5000); // Wait 5 seconds if queue is empty
          continue;
        }

        const queueItem = this.processingQueue.shift();
        await this.processToken(queueItem);
        
        // Rate limiting delay
        await this.telegramPool.waitForRateLimit();
        
      } catch (error) {
        this.logger.error('Error in queue processor:', error);
        await this.delay(10000); // Wait 10 seconds on error
      }
    }
  }

  async processToken(queueItem) {
    const startTime = Date.now();
    let token;

    try {
      token = await Token.findById(queueItem.tokenId);
      if (!token) {
        this.logger.warn(`Token ${queueItem.tokenId} not found during processing`);
        return;
      }

      // Update status to processing
      token.analysisStatus = 'processing';
      await token.incrementAnalysisAttempts();

      this.logger.info(`Analyzing token: ${token.address}`, {
        attempt: token.analysisAttempts,
        sourceId: token.sourceId
      });

      // Emit processing update
      this.io.emit('token-processing', {
        tokenId: token._id,
        address: token.address,
        status: 'processing'
      });

      // Send to Soul Scanner Bot
      const analysisResult = await this.analyzeWithSoulScanner(token.address);
      
      // Parse and save results
      await this.saveAnalysisResults(token, analysisResult);
      
      // Update source stats
      const source = await Source.findById(token.sourceId);
      if (source) {
        await source.incrementTokensAnalyzed();
      }

      const processingTime = Date.now() - startTime;
      this.logger.info(`Token analysis completed: ${token.address}`, {
        processingTime: `${processingTime}ms`,
        symbol: analysisResult.symbol || 'Unknown'
      });

      // Emit completion
      this.io.emit('token-analyzed', {
        tokenId: token._id,
        address: token.address,
        status: 'completed',
        data: analysisResult,
        processingTime
      });

    } catch (error) {
      this.logger.error(`Failed to analyze token ${queueItem.address}:`, error);
      
      if (token) {
        await token.setAnalysisFailed(error);
        
        // Emit failure
        this.io.emit('token-analyzed', {
          tokenId: token._id,
          address: token.address,
          status: 'failed',
          error: error.message
        });
      }
    }
  }

  async analyzeWithSoulScanner(tokenAddress) {
    const client = this.telegramPool.getAvailableClient();
    
    try {
      // Send token address to Soul Scanner Bot
      await client.sendMessage(this.soulScannerBot, { message: tokenAddress });
      
      this.logger.debug(`Sent ${tokenAddress} to Soul Scanner Bot`);
      
      // Wait for response
      const response = await this.waitForSoulScannerResponse(client, tokenAddress);
      
      // Parse the response
      const parsedData = this.parseSoulScannerResponse(response);
      
      return {
        ...parsedData,
        rawResponse: response,
        responseTime: Date.now(),
        analyzedAt: new Date()
      };
      
    } catch (error) {
      this.logger.error(`Soul Scanner analysis failed for ${tokenAddress}:`, error);
      throw error;
    }
  }

  async waitForSoulScannerResponse(client, tokenAddress) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        client.removeEventHandler(handler);
        reject(new Error('Soul Scanner response timeout'));
      }, this.responseTimeout);

      const handler = (event) => {
        if (!event.message || !event.message.text) return;
        
        const message = event.message;
        
        // Check if message is from Soul Scanner Bot
        if (this.isSoulScannerResponse(message, tokenAddress)) {
          clearTimeout(timeout);
          client.removeEventHandler(handler);
          resolve(message.text);
        }
      };

      client.addEventHandler(handler);
    });
  }

  isSoulScannerResponse(message, tokenAddress) {
    // Check if message is from Soul Scanner Bot
    const isSoulScanner = message.fromId && 
                         message.fromId.userId && 
                         message.fromId.userId.toString() === this.soulScannerBot.replace('@', '');
    
    // Check if message contains the token address or relevant data
    const containsToken = message.text.includes(tokenAddress) || 
                         message.text.includes('Symbol:') || 
                         message.text.includes('Price:') ||
                         message.text.includes('Market Cap:');
    
    return isSoulScanner && containsToken;
  }

  parseSoulScannerResponse(responseText) {
    const data = {
      symbol: null,
      name: null,
      price: 0,
      marketCap: null,
      volume24h: null,
      holders: 0,
      liquidity: null,
      fdv: null,
      riskScore: 5
    };

    try {
      const lines = responseText.split('\n');
      
      for (const line of lines) {
        const cleanLine = line.trim();
        
        // Extract symbol
        if (cleanLine.includes('Symbol:')) {
          data.symbol = this.extractValue(cleanLine, 'Symbol:');
        }
        
        // Extract name
        if (cleanLine.includes('Name:')) {
          data.name = this.extractValue(cleanLine, 'Name:');
        }
        
        // Extract price
        if (cleanLine.includes('Price:')) {
          const priceStr = this.extractValue(cleanLine, 'Price:');
          data.price = this.parseNumber(priceStr);
        }
        
        // Extract market cap
        if (cleanLine.includes('Market Cap:')) {
          data.marketCap = this.extractValue(cleanLine, 'Market Cap:');
        }
        
        // Extract volume
        if (cleanLine.includes('Volume:') || cleanLine.includes('24h Volume:')) {
          data.volume24h = this.extractValue(cleanLine, cleanLine.includes('24h Volume:') ? '24h Volume:' : 'Volume:');
        }
        
        // Extract holders
        if (cleanLine.includes('Holders:')) {
          const holdersStr = this.extractValue(cleanLine, 'Holders:');
          data.holders = this.parseNumber(holdersStr);
        }
        
        // Extract liquidity
        if (cleanLine.includes('Liquidity:')) {
          data.liquidity = this.extractValue(cleanLine, 'Liquidity:');
        }
        
        // Extract FDV
        if (cleanLine.includes('FDV:') || cleanLine.includes('Fully Diluted Value:')) {
          data.fdv = this.extractValue(cleanLine, cleanLine.includes('FDV:') ? 'FDV:' : 'Fully Diluted Value:');
        }
      }
      
      // Calculate risk score based on available data
      data.riskScore = this.calculateRiskScore(data);
      
    } catch (error) {
      this.logger.error('Error parsing Soul Scanner response:', error);
    }

    return data;
  }

  extractValue(line, prefix) {
    const index = line.indexOf(prefix);
    if (index === -1) return null;
    
    return line.substring(index + prefix.length).trim().replace(/^[:\-\s]+/, '');
  }

  parseNumber(str) {
    if (!str) return 0;
    
    // Remove common formatting
    const cleaned = str.replace(/[,$\s]/g, '');
    const number = parseFloat(cleaned);
    
    return isNaN(number) ? 0 : number;
  }

  calculateRiskScore(data) {
    let score = 5; // Start with neutral score
    
    // Adjust based on holders
    if (data.holders > 1000) score -= 1;
    if (data.holders < 100) score += 2;
    
    // Adjust based on price
    if (data.price > 0.01) score -= 1;
    if (data.price < 0.000001) score += 1;
    
    // Ensure score is within bounds
    return Math.max(0, Math.min(10, score));
  }

  async saveAnalysisResults(token, analysisResult) {
    try {
      await token.setAnalysisCompleted(analysisResult);
      
      // Store raw response
      token.soulScannerResponse = {
        rawText: analysisResult.rawResponse,
        parsedData: analysisResult,
        responseTime: analysisResult.responseTime,
        timestamp: analysisResult.analyzedAt
      };
      
      await token.save();
      
    } catch (error) {
      this.logger.error('Error saving analysis results:', error);
      throw error;
    }
  }

  async getTokens(filters = {}) {
    const query = {};
    
    if (filters.sourceId) {
      query.sourceId = filters.sourceId;
    }
    
    if (filters.status) {
      query.analysisStatus = filters.status;
    }
    
    if (filters.timeRange) {
      const timeRanges = {
        '1h': 1 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      };
      
      const since = new Date(Date.now() - timeRanges[filters.timeRange]);
      query.discoveredAt = { $gte: since };
    }
    
    return await Token.find(query)
      .populate('sourceId')
      .sort({ discoveredAt: -1 })
      .limit(filters.limit || 100)
      .skip(filters.skip || 0);
  }

  getStats() {
    return {
      queueLength: this.processingQueue.length,
      isProcessing: this.isProcessing
    };
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = TokenAnalyzer;
