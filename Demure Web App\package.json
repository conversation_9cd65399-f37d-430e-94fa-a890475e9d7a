{"name": "demure-web-app", "version": "1.0.0", "description": "Multi-Source Token Analysis Platform with Soul Scanner Integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "dev:frontend": "cd frontend && npm run dev", "dev:full": "concurrently \"npm run dev\" \"npm run dev:frontend\"", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && cd frontend && npm install", "setup": "node install.js", "test": "jest", "lint": "eslint src/", "clean": "rm -rf logs/* exports/* frontend/dist"}, "keywords": ["telegram", "token-analysis", "soul-scanner", "crypto", "pump-tokens"], "author": "Demure Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "mongoose": "^7.5.0", "telegram": "^2.19.8", "exceljs": "^4.4.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "winston": "^3.10.0", "bull": "^4.11.3", "redis": "^4.6.8", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.0", "helmet": "^7.0.0", "rate-limiter-flexible": "^2.4.2", "concurrently": "^8.2.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}